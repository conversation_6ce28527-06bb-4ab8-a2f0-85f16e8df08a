{"last_node_id": 91, "last_link_id": 141, "nodes": [{"id": 54, "type": "VAEDecode", "pos": [2550, 2240], "size": {"0": 210, "1": 46}, "flags": {}, "order": 18, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 78}, {"name": "vae", "type": "VAE", "link": 120}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [80], "slot_index": 0}], "properties": {"Node name for S&R": "VAEDecode"}}, {"id": 52, "type": "K<PERSON><PERSON><PERSON>", "pos": [2150, 2370], "size": {"0": 315, "1": 262}, "flags": {}, "order": 12, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 73}, {"name": "positive", "type": "CONDITIONING", "link": 74}, {"name": "negative", "type": "CONDITIONING", "link": 75}, {"name": "latent_image", "type": "LATENT", "link": 76}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [78], "slot_index": 0}], "properties": {"Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [444793366152418, "fixed", 50, 8, "euler", "normal", 1]}, {"id": 51, "type": "PreviewImage", "pos": [3120, 2360], "size": {"0": 210, "1": 246}, "flags": {}, "order": 29, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 82}], "properties": {"Node name for S&R": "PreviewImage"}}, {"id": 44, "type": "K<PERSON><PERSON><PERSON>", "pos": [2150, 2060], "size": {"0": 315, "1": 262}, "flags": {}, "order": 11, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 62}, {"name": "positive", "type": "CONDITIONING", "link": 63}, {"name": "negative", "type": "CONDITIONING", "link": 64}, {"name": "latent_image", "type": "LATENT", "link": 65}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [68], "slot_index": 0}], "properties": {"Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [444793366152274, "fixed", 50, 8, "euler", "normal", 1]}, {"id": 49, "type": "VAEDecode", "pos": [2540, 2090], "size": {"0": 210, "1": 46}, "flags": {}, "order": 17, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 68}, {"name": "vae", "type": "VAE", "link": 121}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [79], "slot_index": 0}], "properties": {"Node name for S&R": "VAEDecode"}}, {"id": 61, "type": "K<PERSON><PERSON><PERSON>", "pos": [2210, 1270], "size": {"0": 315, "1": 262}, "flags": {}, "order": 13, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 105}, {"name": "positive", "type": "CONDITIONING", "link": 87}, {"name": "negative", "type": "CONDITIONING", "link": 84}, {"name": "latent_image", "type": "LATENT", "link": 85}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [89], "slot_index": 0}], "properties": {"Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [444793366152274, "fixed", 50, 8, "euler", "normal", 1]}, {"id": 56, "type": "ImageBatch", "pos": [2840, 2170], "size": {"0": 210, "1": 46}, "flags": {}, "order": 23, "mode": 0, "inputs": [{"name": "image1", "type": "IMAGE", "link": 79}, {"name": "image2", "type": "IMAGE", "link": 80}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [81, 82], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "ImageBatch"}}, {"id": 58, "type": "AutoCropFaces", "pos": [2860, 1340], "size": {"0": 315, "1": 198}, "flags": {}, "order": 24, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 90}], "outputs": [{"name": "face", "type": "IMAGE", "links": [91], "shape": 3, "slot_index": 0}, {"name": "CROP_DATA", "type": "CROP_DATA", "links": null, "shape": 3, "slot_index": 1}], "properties": {"Node name for S&R": "AutoCropFaces"}, "widgets_values": [5, 0, -1, 1.5, 0.5, 1]}, {"id": 72, "type": "AutoCropFaces", "pos": [3080, 540], "size": {"0": 315, "1": 198}, "flags": {}, "order": 31, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 117}], "outputs": [{"name": "face", "type": "IMAGE", "links": [102], "shape": 3, "slot_index": 0}, {"name": "CROP_DATA", "type": "CROP_DATA", "links": null, "shape": 3, "slot_index": 1}], "properties": {"Node name for S&R": "AutoCropFaces"}, "widgets_values": [5, 0, -1, 1.5, 0.5, 1]}, {"id": 67, "type": "PreviewImage", "pos": [3230, 1140], "size": {"0": 210, "1": 246}, "flags": {}, "order": 25, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 92}], "properties": {"Node name for S&R": "PreviewImage"}}, {"id": 65, "type": "VAEDecode", "pos": [2610, 1290], "size": {"0": 210, "1": 46}, "flags": {}, "order": 19, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 89}, {"name": "vae", "type": "VAE", "link": 109}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [90, 92], "slot_index": 0}], "properties": {"Node name for S&R": "VAEDecode"}}, {"id": 66, "type": "PreviewImage", "pos": [3230, 1430], "size": {"0": 210, "1": 246}, "flags": {}, "order": 30, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 91}], "properties": {"Node name for S&R": "PreviewImage"}}, {"id": 73, "type": "PreviewImage", "pos": [3460, 540], "size": {"0": 210, "1": 246}, "flags": {}, "order": 35, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 102}], "properties": {"Node name for S&R": "PreviewImage"}}, {"id": 68, "type": "K<PERSON><PERSON><PERSON>", "pos": [2190, 260], "size": {"0": 315, "1": 262}, "flags": {}, "order": 14, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 93}, {"name": "positive", "type": "CONDITIONING", "link": 94}, {"name": "negative", "type": "CONDITIONING", "link": 95}, {"name": "latent_image", "type": "LATENT", "link": 96}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [99], "slot_index": 0}], "properties": {"Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [444793366152274, "fixed", 50, 8, "euler", "normal", 1]}, {"id": 70, "type": "VAEDecode", "pos": [2530, 280], "size": {"0": 210, "1": 46}, "flags": {}, "order": 20, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 99}, {"name": "vae", "type": "VAE", "link": 107}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [116], "slot_index": 0}], "properties": {"Node name for S&R": "VAEDecode"}}, {"id": 69, "type": "CLIPTextEncode", "pos": [1630, 270], "size": {"0": 422.84503173828125, "1": 164.31304931640625}, "flags": {}, "order": 5, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 97}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [94, 111], "slot_index": 0}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["Beautiful forest"]}, {"id": 77, "type": "Reroute", "pos": [1970, 440], "size": [75, 26], "flags": {}, "order": 7, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 106}], "outputs": [{"name": "", "type": "VAE", "links": [107, 118], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 46, "type": "EmptyLatentImage", "pos": [501, 1441], "size": {"0": 315, "1": 106}, "flags": {}, "order": 0, "mode": 0, "outputs": [{"name": "LATENT", "type": "LATENT", "links": [65, 76, 85, 96, 113, 126], "slot_index": 0}], "properties": {"Node name for S&R": "EmptyLatentImage"}, "widgets_values": [1024, 1024, 1]}, {"id": 78, "type": "Reroute", "pos": [2009, 1378], "size": [75, 26], "flags": {}, "order": 8, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 108}], "outputs": [{"name": "", "type": "VAE", "links": [109], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 82, "type": "Reroute", "pos": [2030, 2490], "size": [75, 26], "flags": {}, "order": 9, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 119}], "outputs": [{"name": "", "type": "VAE", "links": [120, 121], "slot_index": 0}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 48, "type": "CLIPTextEncode", "pos": [1247, 1461], "size": {"0": 425.27801513671875, "1": 180.6060791015625}, "flags": {}, "order": 3, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 67}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [64, 75, 84, 95, 112, 125], "slot_index": 0}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["text, watermark"]}, {"id": 45, "type": "CheckpointLoaderSimple", "pos": [507, 1591], "size": {"0": 315, "1": 98}, "flags": {}, "order": 1, "mode": 0, "outputs": [{"name": "MODEL", "type": "MODEL", "links": [62, 73, 93, 105, 110, 123], "slot_index": 0}, {"name": "CLIP", "type": "CLIP", "links": [66, 67, 86, 97, 127], "slot_index": 1}, {"name": "VAE", "type": "VAE", "links": [106, 108, 119, 131], "slot_index": 2}], "properties": {"Node name for S&R": "CheckpointLoaderSimple"}, "widgets_values": ["sd_xl_base_1.0.safetensors"]}, {"id": 47, "type": "CLIPTextEncode", "pos": [1630, 2220], "size": {"0": 422.84503173828125, "1": 164.31304931640625}, "flags": {}, "order": 2, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 66}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [63, 74], "slot_index": 0}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["Large crowd of people all facing the camera posing for a company photo."]}, {"id": 64, "type": "CLIPTextEncode", "pos": [1737, 1195], "size": {"0": 422.84503173828125, "1": 164.31304931640625}, "flags": {}, "order": 4, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 86}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [87], "slot_index": 0}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["Female model posing looking at the camera"]}, {"id": 79, "type": "K<PERSON><PERSON><PERSON>", "pos": [2190, 560], "size": {"0": 315, "1": 262}, "flags": {}, "order": 15, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 110}, {"name": "positive", "type": "CONDITIONING", "link": 111}, {"name": "negative", "type": "CONDITIONING", "link": 112}, {"name": "latent_image", "type": "LATENT", "link": 113}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [114], "slot_index": 0}], "properties": {"Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [444793366152415, "fixed", 50, 8, "euler", "normal", 1]}, {"id": 81, "type": "VAEDecode", "pos": [2540, 570], "size": {"0": 210, "1": 46}, "flags": {}, "order": 21, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 114}, {"name": "vae", "type": "VAE", "link": 118}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [115], "slot_index": 0}], "properties": {"Node name for S&R": "VAEDecode"}}, {"id": 80, "type": "ImageBatch", "pos": [2810, 580], "size": {"0": 210, "1": 46}, "flags": {}, "order": 26, "mode": 0, "inputs": [{"name": "image1", "type": "IMAGE", "link": 116}, {"name": "image2", "type": "IMAGE", "link": 115}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [117, 122], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "ImageBatch"}}, {"id": 40, "type": "PreviewImage", "pos": [3530, 2070], "size": [140.9676944827229, 232.36873662566404], "flags": {}, "order": 34, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 53}], "properties": {"Node name for S&R": "PreviewImage"}}, {"id": 87, "type": "Reroute", "pos": [1990, -170], "size": [75, 26], "flags": {}, "order": 10, "mode": 0, "inputs": [{"name": "", "type": "*", "link": 131}], "outputs": [{"name": "", "type": "VAE", "links": [129]}], "properties": {"showOutputText": false, "horizontal": false}}, {"id": 84, "type": "CLIPTextEncode", "pos": [1650, -340], "size": {"0": 422.84503173828125, "1": 164.31304931640625}, "flags": {}, "order": 6, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 127}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [124], "slot_index": 0}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["Beautiful forest"]}, {"id": 86, "type": "AutoCropFaces", "pos": [3050, -320], "size": {"0": 315, "1": 198}, "flags": {}, "order": 27, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 141}], "outputs": [{"name": "face", "type": "IMAGE", "links": [140], "shape": 3, "slot_index": 0}, {"name": "CROP_DATA", "type": "CROP_DATA", "links": null, "shape": 3, "slot_index": 1}], "properties": {"Node name for S&R": "AutoCropFaces"}, "widgets_values": [5, 0, -1, 1.5, 0.5, 1]}, {"id": 91, "type": "PreviewImage", "pos": [3410, -320], "size": {"0": 210, "1": 246}, "flags": {}, "order": 33, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 140}], "properties": {"Node name for S&R": "PreviewImage"}}, {"id": 71, "type": "PreviewImage", "pos": [3450, 260], "size": {"0": 210, "1": 246}, "flags": {}, "order": 32, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 122}], "properties": {"Node name for S&R": "PreviewImage"}}, {"id": 36, "type": "AutoCropFaces", "pos": [3120, 2130], "size": {"0": 315, "1": 198}, "flags": {}, "order": 28, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 81}], "outputs": [{"name": "face", "type": "IMAGE", "links": [53], "shape": 3, "slot_index": 0}, {"name": "CROP_DATA", "type": "CROP_DATA", "links": null, "shape": 3, "slot_index": 1}], "properties": {"Node name for S&R": "AutoCropFaces"}, "widgets_values": [50, 5, 15, 1.5, 0.5, 1]}, {"id": 85, "type": "VAEDecode", "pos": [2550, -330], "size": {"0": 210, "1": 46}, "flags": {}, "order": 22, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 128}, {"name": "vae", "type": "VAE", "link": 129}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [141], "slot_index": 0}], "properties": {"Node name for S&R": "VAEDecode"}}, {"id": 83, "type": "K<PERSON><PERSON><PERSON>", "pos": [2200, -350], "size": {"0": 315, "1": 262}, "flags": {}, "order": 16, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 123}, {"name": "positive", "type": "CONDITIONING", "link": 124}, {"name": "negative", "type": "CONDITIONING", "link": 125}, {"name": "latent_image", "type": "LATENT", "link": 126}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [128], "slot_index": 0}], "properties": {"Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [444793366152357, "fixed", 50, 8, "euler", "normal", 1]}], "links": [[53, 36, 0, 40, 0, "IMAGE"], [62, 45, 0, 44, 0, "MODEL"], [63, 47, 0, 44, 1, "CONDITIONING"], [64, 48, 0, 44, 2, "CONDITIONING"], [65, 46, 0, 44, 3, "LATENT"], [66, 45, 1, 47, 0, "CLIP"], [67, 45, 1, 48, 0, "CLIP"], [68, 44, 0, 49, 0, "LATENT"], [73, 45, 0, 52, 0, "MODEL"], [74, 47, 0, 52, 1, "CONDITIONING"], [75, 48, 0, 52, 2, "CONDITIONING"], [76, 46, 0, 52, 3, "LATENT"], [78, 52, 0, 54, 0, "LATENT"], [79, 49, 0, 56, 0, "IMAGE"], [80, 54, 0, 56, 1, "IMAGE"], [81, 56, 0, 36, 0, "IMAGE"], [82, 56, 0, 51, 0, "IMAGE"], [84, 48, 0, 61, 2, "CONDITIONING"], [85, 46, 0, 61, 3, "LATENT"], [86, 45, 1, 64, 0, "CLIP"], [87, 64, 0, 61, 1, "CONDITIONING"], [89, 61, 0, 65, 0, "LATENT"], [90, 65, 0, 58, 0, "IMAGE"], [91, 58, 0, 66, 0, "IMAGE"], [92, 65, 0, 67, 0, "IMAGE"], [93, 45, 0, 68, 0, "MODEL"], [94, 69, 0, 68, 1, "CONDITIONING"], [95, 48, 0, 68, 2, "CONDITIONING"], [96, 46, 0, 68, 3, "LATENT"], [97, 45, 1, 69, 0, "CLIP"], [99, 68, 0, 70, 0, "LATENT"], [102, 72, 0, 73, 0, "IMAGE"], [105, 45, 0, 61, 0, "MODEL"], [106, 45, 2, 77, 0, "*"], [107, 77, 0, 70, 1, "VAE"], [108, 45, 2, 78, 0, "*"], [109, 78, 0, 65, 1, "VAE"], [110, 45, 0, 79, 0, "MODEL"], [111, 69, 0, 79, 1, "CONDITIONING"], [112, 48, 0, 79, 2, "CONDITIONING"], [113, 46, 0, 79, 3, "LATENT"], [114, 79, 0, 81, 0, "LATENT"], [115, 81, 0, 80, 1, "IMAGE"], [116, 70, 0, 80, 0, "IMAGE"], [117, 80, 0, 72, 0, "IMAGE"], [118, 77, 0, 81, 1, "VAE"], [119, 45, 2, 82, 0, "*"], [120, 82, 0, 54, 1, "VAE"], [121, 82, 0, 49, 1, "VAE"], [122, 80, 0, 71, 0, "IMAGE"], [123, 45, 0, 83, 0, "MODEL"], [124, 84, 0, 83, 1, "CONDITIONING"], [125, 48, 0, 83, 2, "CONDITIONING"], [126, 46, 0, 83, 3, "LATENT"], [127, 45, 1, 84, 0, "CLIP"], [128, 83, 0, 85, 0, "LATENT"], [129, 87, 0, 85, 1, "VAE"], [131, 45, 2, 87, 0, "*"], [140, 86, 0, 91, 0, "IMAGE"], [141, 85, 0, 86, 0, "IMAGE"]], "groups": [], "config": {}, "extra": {"ds": {"scale": 0.5730855330116886, "offset": [-309.80067971195854, -137.54286569310958]}}, "version": 0.4}