import base64
import cv2
import io
import numpy as np
import os
import requests
import sys
import subprocess
from pathlib import Path
from PIL import Image

# Import the centralized AI provider and config
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from ai_provider import get_provider
from ai_provider.config_manager import get_prompt

# Get the absolute path to the ComfyUI_AutoCropFaces directory
current_dir = os.path.dirname(os.path.abspath(__file__))
repo_root = os.path.dirname(current_dir)  # Get parent directory of training
autocrop_path = os.path.join(repo_root, 'ComfyUI_AutoCropFaces')

sys.path.append(autocrop_path)

from Pytorch_Retinaface.pytorch_retinaface import Pytorch_RetinaFace


def get_system_prompt():
    # Get system prompt from centralized config
    prompt = get_prompt("training_utilities_system")
    if prompt:
        return prompt

    # Fallback to original prompt if config not available
    return """You are a concise image captioning assistant.

Your task is to provide a brief caption for the given image (around 3 sentences).
Focus on describing:
- The subject (person, character, etc.)
- Physical appearance and attributes
- Clothing and accessories
- Facial features and expression

IMPORTANT: Completely IGNORE the background - do not mention it at all.
Remain objective – do not reference known characters, franchises, or people, even if recognizable.
Avoid making assumptions about things that aren't visible in the image.
"""


def image_to_base64(image):
    """Convert a PIL image to base64 encoded string."""
    if not isinstance(image, Image.Image):
        image = Image.fromarray(image)

    # Convert to RGB if it has an alpha channel
    if image.mode == 'RGBA':
        image = image.convert('RGB')

    buffered = io.BytesIO()
    image.save(buffered, format="PNG")
    img_str = base64.b64encode(buffered.getvalue()).decode("utf-8")
    return img_str


def generate_caption(image):
    """Generate a detailed caption for the image using Gemini Vision"""
    # Get the centralized AI provider
    provider = get_provider()

    # Create the caption prompt
    prompt = f"{get_system_prompt()}\n\nPlease provide a detailed caption for this image."

    # Generate caption using Gemini Vision
    caption = provider.caption_image(image, prompt)

    return caption.strip()


def upscale_image(image_path, output_path=None, scale=1.5, model="RealESRGAN_x4plus", face_enhance=True,
                  output_format="png"):
    """
    Upscale an image using local ScaleX-CLI implementation
    
    Args:
        image_path: Path to the image file
        output_path: Path where the upscaled image will be saved (default: None, will return PIL Image)
        scale: Scaling factor (default: 1.5)
        model: Model to use (ignored, using ScaleX models)
        face_enhance: Whether to enhance faces (default: True)
        output_format: Output image format (default: "png")
    
    Returns:
        If output_path is provided: Path to the saved image
        If output_path is None: PIL Image of the upscaled result
    """
    image_path = Path(image_path)
    if not image_path.exists():
        raise FileNotFoundError(f"Input image not found: {image_path}")
    
    # Get ScaleX-CLI directory
    repo_root = Path(__file__).parent.parent
    scalex_dir = repo_root / "ScaleX-CLI"
    inference_script = scalex_dir / "inference_scalex.py"
    
    if not inference_script.exists():
        raise FileNotFoundError(f"ScaleX inference script not found: {inference_script}")
    
    # Create temporary output if none provided
    temp_output = False
    if output_path is None:
        temp_output = True
        output_path = image_path.parent / f"{image_path.stem}_upscaled_temp{image_path.suffix}"
    else:
        output_path = Path(output_path)
    
    # Ensure output directory exists
    output_path.parent.mkdir(parents=True, exist_ok=True)
    
    # Map scale to upscale factor (ScaleX uses integer factors)
    upscale_factor = max(1, int(round(scale)))
    
    # Determine face and background enhancement settings
    face_enhance_version = "v1.4" if face_enhance else "none"
    bg_enhance_version = "x2"  # Default background enhancement
    
    # Build ScaleX command
    cmd = [
        "python", str(inference_script),
        "--input", str(image_path),
        "--output", str(output_path.parent),
        "--face-enhance", face_enhance_version,
        "--bg-enhance", bg_enhance_version,
        "--upscale", str(upscale_factor),
        "--device", "auto",
        "--ext", output_format
    ]
    
    print(f"🚀 Upscaling with ScaleX-CLI: {image_path.name}")
    
    try:
        # Run ScaleX
        result = subprocess.run(
            cmd,
            cwd=scalex_dir,
            capture_output=True,
            text=True,
            check=True
        )
        
        print(f"✅ ScaleX completed successfully")
        
        # ScaleX might save with a different name, find the actual output
        possible_outputs = [
            output_path,
            output_path.parent / f"{image_path.stem}_restored{image_path.suffix}",
            output_path.parent / f"{image_path.stem}_enhanced{image_path.suffix}",
            output_path.parent / f"{image_path.stem}_upscaled{image_path.suffix}",
        ]
        
        actual_output = None
        for possible_output in possible_outputs:
            if possible_output.exists():
                actual_output = possible_output
                break
        
        if actual_output is None:
            # List directory contents to debug
            print(f"⚠️ Could not find output. Contents of {output_path.parent}:")
            for item in output_path.parent.iterdir():
                if item.is_file():
                    print(f"  - {item.name}")
            raise FileNotFoundError("Could not find ScaleX output file")
        
        # If we need to return PIL Image
        if temp_output:
            upscaled_image = Image.open(actual_output)
            # Clean up temp file
            actual_output.unlink()
            return upscaled_image
        else:
            # Rename to expected output path if different
            if actual_output != output_path:
                actual_output.rename(output_path)
            return str(output_path)
            
    except subprocess.CalledProcessError as e:
        print(f"❌ ScaleX failed with error: {e}")
        print(f"📝 Stdout: {e.stdout}")
        print(f"📝 Stderr: {e.stderr}")
        raise RuntimeError(f"ScaleX upscaling failed: {e}")


def rectangle_to_square(image, background_color=(128, 128, 128)):
    """
    Convert a rectangular image to a square by adding padding with a background color.
    If the image is already square, returns the original image.
    
    Args:
        image: PIL Image or numpy array
        background_color: RGB tuple for the background color (default: gray)
    
    Returns:
        A square PIL Image with the original image centered
    """
    if not isinstance(image, Image.Image):
        image = Image.fromarray(image)

    # Get the original dimensions
    width, height = image.size

    # If image is already square, return the original
    if width == height:
        return image

    # Calculate the target size (use the larger dimension)
    target_size = max(width, height)

    # Create a new square image with the background color
    result = Image.new('RGB', (target_size, target_size), background_color)

    # Calculate position to paste the original image (centered)
    x_offset = (target_size - width) // 2
    y_offset = (target_size - height) // 2

    # Paste the original image onto the square background
    result.paste(image, (x_offset, y_offset))

    return result


def crop_face(image_path, output_dir, output_name, scale_factor=4.0):
    image = Image.open(image_path).convert("RGB")

    img_raw = cv2.cvtColor(np.array(image), cv2.COLOR_RGB2BGR)
    img_raw = img_raw.astype(np.float32)

    rf = Pytorch_RetinaFace(
        cfg='mobile0.25',
        pretrained_path='./weights/mobilenet0.25_Final.pth',
        confidence_threshold=0.02,
        nms_threshold=0.4,
        vis_thres=0.6
    )

    dets = rf.detect_faces(img_raw)
    print("Dets: ", dets)

    # Instead of asserting, handle multiple faces gracefully
    if len(dets) == 0:
        print("No faces detected!")
        return False

    # If multiple faces detected, use the one with highest confidence
    if len(dets) > 1:
        print(f"Warning: {len(dets)} faces detected, using the one with highest confidence")
        # Assuming dets is a list of [bbox, landmark, score] and we want to sort by score
        dets = sorted(dets, key=lambda x: x[2], reverse=True)  # Sort by confidence score
        # Just keep the highest confidence detection
        dets = [dets[0]]

    # Pass the scale_factor to center_and_crop_rescale for adjustable crop size
    try:
        # Unpack the tuple correctly - the function returns (cropped_imgs, bbox_infos)
        cropped_imgs, bbox_infos = rf.center_and_crop_rescale(img_raw, dets, shift_factor=0.45,
                                                              scale_factor=scale_factor)

        # Assuming cropped_imgs is a list or tuple (cropped_imgs, bbox_infos)
        for i, (cropped_img, bbox_info) in enumerate(zip(cropped_imgs, bbox_infos)):
            # Convert BGR to RGB
            cropped_img_rgb = cv2.cvtColor(cropped_img, cv2.COLOR_BGR2RGB)

            # Convert to PIL Image
            cropped_pil = Image.fromarray(cropped_img_rgb.astype(np.uint8))

            # Ensure output directory exists
            os.makedirs(output_dir, exist_ok=True)

            # Save to output path
            out_name = output_name if isinstance(output_name, str) else f"face_{i}.png"
            output_path = os.path.join(output_dir, out_name)
            cropped_pil.save(output_path)

            print(f"Face cropped and saved to {output_path}")
            return output_path
    except Exception as e:
        print(f"Error cropping face: {e}")
        return False


def get_upscale_factor(image: str = None, input_size=None, target_size=768):
    """Calculate the scale factor needed to upscale an image to approximately targetxtarget"""

    if input_size is None:
        image = Image.open(image).convert("RGB")
        width, height = image.size
    else:
        width, height = input_size

    width_scale = target_size / width
    height_scale = target_size / height

    scale_factor = max(width_scale, height_scale)
    return scale_factor


def resize_if_large(image, max_size=1536):
    """
    Resize a square image if its dimensions exceed the specified maximum size.
    
    Args:
        image: PIL Image object (assumed to be square)
        max_size: Maximum allowed dimension (width/height) in pixels
        
    Returns:
        PIL Image: Resized image if needed, or original image if already small enough
    """
    if not isinstance(image, Image.Image):
        image = Image.fromarray(image)

    width, height = image.size

    # Verify image is square (should be after rectangle_to_square)
    if width != height:
        print(f"Warning: Expected square image but got {width}x{height}")

    # If image is already small enough, return the original
    if width <= max_size:
        return image

    # Resize the square image
    resized_image = image.resize((max_size, max_size), Image.LANCZOS)

    return resized_image
