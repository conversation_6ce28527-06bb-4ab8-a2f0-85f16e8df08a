import aiohttp
import asyncio
import base64
import os
import sys
import torch
import numpy as np
from PIL import Image
from dotenv import load_dotenv
from typing import List
import cv2

# Import the centralized AI provider and config
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from ai_provider import get_provider
from ai_provider.config_manager import get_prompt

# Add PuLID-FLUX to path
sys.path.append(os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'PuLID-FLUX'))
from local_pulid import generate_pulid_batch

load_dotenv()


def generate_pulidflux_prompts(
        image_path: str,
        person_description: str,
        num_prompts: int = 5
) -> List[str]:
    """
    Generate text prompts for PuLID-Flux image generation using Gemini Vision,
    with example prompts to guide the model's output style.

    Args:
        image_path: Path to the image of the person
        person_description: Description of the person including appearance, style, and personality
        num_prompts: Number of prompts to generate (default: 5)

    Returns:
        List of generated text prompts

    Raises:
        FileNotFoundError: If image_path doesn't exist
        ValueError: If invalid parameters provided
        RuntimeError: If AI provider fails
    """
    # Input validation
    if not image_path or not os.path.exists(image_path):
        raise FileNotFoundError(f"Image file not found: {image_path}")
    
    if not person_description or not person_description.strip():
        raise ValueError("Person description cannot be empty")
    
    if num_prompts < 1 or num_prompts > 50:
        raise ValueError(f"num_prompts must be between 1 and 50, got {num_prompts}")

    try:
        # Get AI provider with error handling
        provider = get_provider()
        if not provider:
            raise RuntimeError("AI provider not available")

        # Read image with error handling
        try:
            with open(image_path, "rb") as image_file:
                image_bytes = image_file.read()
            
            if len(image_bytes) == 0:
                raise ValueError(f"Image file is empty: {image_path}")
                
        except (IOError, OSError) as e:
            raise RuntimeError(f"Failed to read image file {image_path}: {e}")

        # Get system prompt from centralized config
        try:
            system_prompt = get_prompt("pulid_flux_system", num_prompts=num_prompts)
        except Exception as e:
            print(f"⚠️ Error loading system prompt from config: {e}")
            system_prompt = None

        if not system_prompt:
            print("⚠️ PuLID Flux system prompt not found in config, using fallback")
            # Fallback to original prompt if config not available
            system_prompt = """You are an expert prompt engineer specializing in creating high-quality text prompts for AI image generation. Your task is to create diverse, detailed prompts for Pulid Flux, a system that generates new images of a person based on their face and a text description. All images of people are AI generated.

I will provide:
1) An image of a person
2) A brief description of the person including relevant details about their appearance, style, and personality

Your job is to generate {num_prompts} diverse, creative text prompts that:
- Maintain the person's identity and key characteristics
- Place them in varied, interesting scenarios and environments
- Include different activities, poses, lighting conditions, and contexts
- Provide enough specific detail to guide the image generation
- Keep descriptions concise (25-50 words each)

Each prompt should be on a new line and prefixed with 'PROMPT:'. Focus on scenarios that would make for visually compelling images and showcase the person in different contexts.

Here are examples of the style and quality of prompts I'm looking for:

PROMPT: "The person elegantly dancing barefoot on a moonlit beach, her intricate dress flowing gently in the ocean breeze."
PROMPT: "The person carefully arranging fresh flowers into a vibrant bouquet at a rustic farmer's market stall, sunlight accentuating her expressive features."
PROMPT: "The person standing confidently at the bow of an old wooden sailing ship, looking ahead determinedly as waves splash softly around her."
PROMPT: "The person in her ornate dress seated at an antique piano, immersed deeply in playing a romantic classical piece, illuminated by vintage candlelight."
PROMPT: "The person gently releasing colorful paper lanterns into a twilight sky during a lively cultural festival, her arms gracefully raised upward."

Format your response as {num_prompts} lines, each starting with 'PROMPT:' followed by the prompt text.""".format(
                num_prompts=num_prompts)
        else:
            print("✅ Using centralized PuLID Flux system prompt from config")

        # Create the full prompt for Gemini
        full_prompt = f"{system_prompt}\n\nHere's a description of the person: {person_description}\n\nPlease analyze the provided image and generate {num_prompts} diverse prompts."

        # Generate prompts using Gemini Vision with retry logic
        max_retries = 3
        for attempt in range(max_retries):
            try:
                generated_text = provider.caption_image(image_bytes, full_prompt)
                if generated_text and generated_text.strip():
                    break
                else:
                    raise RuntimeError("Empty response from AI provider")
            except Exception as e:
                if attempt == max_retries - 1:
                    raise RuntimeError(f"AI provider failed after {max_retries} attempts: {e}")
                print(f"⚠️ Attempt {attempt + 1} failed, retrying: {e}")
                continue

        print(f"📝 Generated prompts response: {generated_text[:200]}...")

        # Parse prompts with error handling
        prompts = []
        if generated_text:
            for line in generated_text.split("\n"):
                line = line.strip()
                if line.startswith("PROMPT:"):
                    prompt_text = line[len("PROMPT:"):].strip()
                    if prompt_text:  # Only add non-empty prompts
                        prompts.append(prompt_text)

        # Ensure we have enough prompts
        if len(prompts) < num_prompts:
            print(f"⚠️ Only generated {len(prompts)} prompts, expected {num_prompts}")
            # Add fallback prompts if needed
            fallback_prompts = [
                "The person in a professional portrait style, well-lit studio setting",
                "The person in casual attire, natural outdoor lighting",
                "The person in elegant formal wear, sophisticated environment"
            ]
            while len(prompts) < num_prompts and fallback_prompts:
                prompts.append(fallback_prompts.pop(0))

        return prompts[:num_prompts]

    except Exception as e:
        print(f"❌ Error generating PuLID-Flux prompts: {e}")
        # Return fallback prompts in case of complete failure
        fallback_prompts = [
            f"A portrait of the person, {person_description}",
            f"The person in a natural setting, {person_description}",
            f"The person in professional attire, {person_description}"
        ]
        return fallback_prompts[:num_prompts]


async def download_image(url: str, save_path: str):
    async with aiohttp.ClientSession() as session:
        async with session.get(url) as response:
            if response.status == 200:
                with open(save_path, 'wb') as f:
                    f.write(await response.read())


def generate_pulidflux_images_local(prompts: List[str], image_path: str, output_dir: str):
    """
    Generate PuLID-Flux images locally using the integrated PuLID-FLUX implementation
    
    Args:
        prompts: List of text prompts for generation
        image_path: Path to reference image
        output_dir: Directory to save generated images
        
    Returns:
        List of paths to generated images
        
    Raises:
        ValueError: If invalid inputs provided
        FileNotFoundError: If reference image not found
        RuntimeError: If generation fails completely
    """
    # Input validation
    if not prompts or len(prompts) == 0:
        raise ValueError("Prompts list cannot be empty")
    
    if not image_path or not os.path.exists(image_path):
        raise FileNotFoundError(f"Reference image not found: {image_path}")
    
    if not output_dir:
        raise ValueError("Output directory cannot be empty")

    try:
        # Ensure output directory exists
        os.makedirs(output_dir, exist_ok=True)
        
        # Initialize device with fallback
        device = "cuda" if torch.cuda.is_available() else "cpu"
        print(f"🖥️ Using device: {device}")
        
        # Validate reference image
        try:
            test_image = cv2.imread(image_path)
            if test_image is None:
                raise ValueError(f"Could not load reference image: {image_path}")
        except Exception as e:
            raise RuntimeError(f"Error validating reference image: {e}")

        print("🚀 Generating images with local PuLID-FLUX...")
        
        try:
            # Import with error handling
            try:
                from local_pulid import generate_pulid_batch
            except ImportError as e:
                raise RuntimeError(f"Failed to import PuLID-FLUX module: {e}")
            
            # Use the local PuLID implementation
            output_image_paths = generate_pulid_batch(
                prompts=prompts,
                reference_image_path=image_path,
                output_dir=output_dir,
                device=device,
                width=1024,
                height=1024,
                num_inference_steps=20,
                guidance_scale=4.0,
                negative_prompt="bad quality, worst quality, text, signature, watermark, extra limbs",
                id_weight=1.0
            )
            
            # Validate output
            if not output_image_paths or len(output_image_paths) == 0:
                raise RuntimeError("PuLID generation returned no images")
            
            # Verify generated files exist
            valid_paths = []
            for path in output_image_paths:
                if os.path.exists(path) and os.path.getsize(path) > 0:
                    valid_paths.append(path)
                else:
                    print(f"⚠️ Generated file missing or empty: {path}")
            
            if valid_paths:
                print(f"✅ Generated {len(valid_paths)} images locally")
                return valid_paths
            else:
                raise RuntimeError("All generated images are missing or corrupted")
                
        except ImportError as e:
            print(f"❌ PuLID-FLUX module import failed: {e}")
            raise RuntimeError(f"PuLID-FLUX not properly installed: {e}")
        except Exception as e:
            print(f"❌ Error in local PuLID generation: {e}")
            print("⚠️ Note: Full PuLID-FLUX requires FLUX model weights and additional setup")
            raise
        
    except Exception as e:
        print(f"❌ Critical error in PuLID generation: {e}")
        
        # Fallback: create placeholder images
        print("🔄 Creating placeholder images as fallback...")
        output_image_paths = []
        
        try:
            reference_image = cv2.imread(image_path)
            if reference_image is not None:
                for i, prompt in enumerate(prompts):
                    try:
                        output_path = os.path.join(output_dir, f"pulid_placeholder_{i}.jpg")
                        success = cv2.imwrite(output_path, reference_image)
                        if success and os.path.exists(output_path):
                            output_image_paths.append(output_path)
                            print(f"📝 Placeholder saved: {output_path}")
                        else:
                            print(f"❌ Failed to save placeholder: {output_path}")
                    except Exception as save_error:
                        print(f"❌ Error saving placeholder {i}: {save_error}")
                        continue
            else:
                print(f"❌ Could not load reference image for fallback: {image_path}")
        except Exception as fallback_error:
            print(f"❌ Fallback creation failed: {fallback_error}")
        
        if output_image_paths:
            print(f"⚠️ Returning {len(output_image_paths)} placeholder images")
            return output_image_paths
        else:
            raise RuntimeError("Both PuLID generation and fallback creation failed")


async def generate_pulidflux_images(prompts: List[str], image_path: str, output_dir: str):
    """
    Wrapper to maintain compatibility - now uses local implementation
    """
    return generate_pulidflux_images_local(prompts, image_path, output_dir)


def generate_synthetic_images(
        image_path: str,
        description: str,
        num_images: int,
        output_dir: str
):
    """
    Generate prompts and create images based on the given parameters.

    Args:
        image_path: Path to the image of the person
        description: Description of the person including appearance, style, and personality
        num_images: Number of images to generate
        output_dir: Directory to save the generated images
    """
    prompts = generate_pulidflux_prompts(
        image_path=image_path,
        person_description=description,
        num_prompts=num_images
    )

    output_image_paths = asyncio.run(generate_pulidflux_images(prompts, image_path, output_dir))
    return output_image_paths
