#!/usr/bin/env python3
"""
Test script to verify the AI provider migration works correctly.
This script tests the centralized Gemini provider functionality.
"""

import os
import sys
from PIL import Image
import io
import base64

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_ai_provider_import():
    """Test that the AI provider can be imported and initialized."""
    print("🧪 Testing AI provider import...")
    try:
        from ai_provider import get_provider, AIProviderConfig
        provider = get_provider()
        print("✅ AI provider imported and initialized successfully")
        return True
    except Exception as e:
        print(f"❌ Failed to import AI provider: {e}")
        return False

def test_configuration():
    """Test that configuration works correctly."""
    print("🧪 Testing configuration...")
    try:
        from ai_provider.config import AIProviderConfig
        
        # Test environment variable loading
        config = AIProviderConfig.from_environment()
        print(f"✅ Configuration loaded successfully")
        print(f"   - Has API key: {'Yes' if config.api_key else 'No'}")
        print(f"   - Text model: {config.default_text_model}")
        print(f"   - Vision model: {config.default_vision_model}")
        return True
    except Exception as e:
        print(f"❌ Configuration test failed: {e}")
        return False

def test_health_check():
    """Test the provider health check."""
    print("🧪 Testing provider health check...")
    try:
        from ai_provider import get_provider
        provider = get_provider()
        
        # This will test if the API key is valid
        is_healthy = provider.health_check()
        if is_healthy:
            print("✅ Provider health check passed")
        else:
            print("⚠️  Provider health check failed (check API key)")
        return is_healthy
    except Exception as e:
        print(f"❌ Health check failed: {e}")
        return False

def test_text_generation():
    """Test basic text generation."""
    print("🧪 Testing text generation...")
    try:
        from ai_provider import get_provider
        provider = get_provider()
        
        response = provider.generate_text("Say 'Hello, migration test successful!' and nothing else.")
        print(f"✅ Text generation successful: {response[:50]}...")
        return True
    except Exception as e:
        print(f"❌ Text generation failed: {e}")
        return False

def create_test_image():
    """Create a simple test image."""
    # Create a simple 100x100 red square
    img = Image.new('RGB', (100, 100), color='red')
    return img

def test_image_captioning():
    """Test image captioning functionality."""
    print("🧪 Testing image captioning...")
    try:
        from ai_provider import get_provider
        provider = get_provider()
        
        # Create a test image
        test_img = create_test_image()
        
        # Convert to bytes
        img_byte_arr = io.BytesIO()
        test_img.save(img_byte_arr, format='PNG')
        img_bytes = img_byte_arr.getvalue()
        
        caption = provider.caption_image(img_bytes, "Describe this image briefly.")
        print(f"✅ Image captioning successful: {caption[:50]}...")
        return True
    except Exception as e:
        print(f"❌ Image captioning failed: {e}")
        return False

def test_training_utilities():
    """Test the training utilities integration."""
    print("🧪 Testing training utilities integration...")
    try:
        from training.utilities import generate_caption
        
        # Create a test image
        test_img = create_test_image()
        
        caption = generate_caption(test_img)
        print(f"✅ Training utilities integration successful: {caption[:50]}...")
        return True
    except Exception as e:
        print(f"❌ Training utilities integration failed: {e}")
        return False

def test_lora_captioner():
    """Test the LoRA captioner integration."""
    print("🧪 Testing LoRA captioner integration...")
    try:
        sys.path.append(os.path.join(os.path.dirname(__file__), 'LoRACaptioner'))
        from caption import caption_images
        
        # Create test images
        test_images = [create_test_image(), create_test_image()]
        
        captions = caption_images(test_images)
        print(f"✅ LoRA captioner integration successful: {len(captions)} captions generated")
        if captions:
            print(f"   Sample caption: {captions[0][:50]}...")
        return True
    except Exception as e:
        print(f"❌ LoRA captioner integration failed: {e}")
        return False

def main():
    """Run all tests."""
    print("🚀 Starting AI Provider Migration Tests")
    print("=" * 50)
    
    tests = [
        test_ai_provider_import,
        test_configuration,
        test_health_check,
        test_text_generation,
        test_image_captioning,
        test_training_utilities,
        test_lora_captioner,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ Test {test.__name__} crashed: {e}")
        print()
    
    print("=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Migration is successful.")
        return 0
    else:
        print("⚠️  Some tests failed. Check the output above for details.")
        return 1

if __name__ == "__main__":
    exit(main())
