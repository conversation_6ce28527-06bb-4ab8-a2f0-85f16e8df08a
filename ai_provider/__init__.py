"""
Centralized AI Provider Service

This module provides a unified interface for all AI operations in the project
using Google Gemini. All AI operations (text generation, image captioning,
image generation) are handled through the centralized Gemini provider.
"""

from .gemini_provider import GeminiProvider
from .config import AIProviderConfig
from .config_manager import Config<PERSON><PERSON><PERSON>, get_config_manager, get_ai_config, get_prompt
from .exceptions import AIProviderError, APIError, RateLimitError

__all__ = [
    'GeminiProvider',
    'AIProviderConfig',
    'ConfigManager',
    'get_config_manager',
    'get_ai_config',
    'get_prompt',
    'AIProviderError',
    'APIError',
    'RateLimitError',
    'get_provider'
]

# Global provider instance
_provider_instance = None

def get_provider():
    """Get the global AI provider instance."""
    global _provider_instance
    if _provider_instance is None:
        _provider_instance = GeminiProvider()
    return _provider_instance

def set_provider(provider):
    """Set the global AI provider instance."""
    global _provider_instance
    _provider_instance = provider
