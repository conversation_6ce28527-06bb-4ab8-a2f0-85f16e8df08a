"""
Configuration management for the AI provider service.
"""

import os
from typing import Optional, Dict, Any
from dataclasses import dataclass

@dataclass
class AIProviderConfig:
    """Configuration for the AI provider service."""

    # API Configuration
    api_key: Optional[str] = None

    # Model Configuration
    text_model: str = "gemini-1.5-flash"
    vision_model: str = "gemini-1.5-flash"

    # Generation Configuration
    temperature: float = 0.7
    max_output_tokens: int = 2048

    # Rate Limiting
    requests_per_second: float = 15.0
    
    @classmethod
    def from_environment(cls) -> 'AIProviderConfig':
        """Create configuration from environment variables."""

        # Try different environment variable names for API key
        api_key = (
            os.getenv('GOOGLE_API_KEY') or
            os.getenv('GEMINI_API_KEY') or
            os.getenv('GOOGLE_GENAI_API_KEY')
        )

        return cls(
            api_key=api_key,
            text_model=os.getenv('AI_PROVIDER_DEFAULT_TEXT_MODEL', 'gemini-1.5-flash'),
            vision_model=os.getenv('AI_PROVIDER_DEFAULT_VISION_MODEL', 'gemini-1.5-flash'),
            temperature=float(os.getenv('AI_PROVIDER_TEMPERATURE', '0.7')),
            max_output_tokens=int(os.getenv('AI_PROVIDER_MAX_TOKENS', '2048')),
            requests_per_second=float(os.getenv('AI_PROVIDER_RATE_LIMIT', '15.0'))
        )
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert configuration to dictionary."""
        return {
            'api_key': self.api_key,
            'text_model': self.text_model,
            'vision_model': self.vision_model,
            'temperature': self.temperature,
            'max_output_tokens': self.max_output_tokens,
            'requests_per_second': self.requests_per_second
        }
