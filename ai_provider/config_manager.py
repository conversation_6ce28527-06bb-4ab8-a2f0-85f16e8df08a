"""
Unified Configuration Manager

This module provides centralized configuration management for AI providers and prompts,
loading settings from YAML files with environment variable substitution.
"""

import os
import re
import yaml
from typing import Dict, Any, Optional
from dataclasses import dataclass, field
from pathlib import Path

@dataclass
class PromptConfig:
    """Configuration for a specific prompt."""
    id: str
    description: str
    content: str
    
    def format(self, **kwargs) -> str:
        """Format the prompt content with provided variables."""
        return self.content.format(**kwargs)

@dataclass
class AIProviderConfig:
    """Unified AI provider configuration."""
    # API Configuration
    api_key: Optional[str] = None
    project_id: Optional[str] = None
    location: Optional[str] = None
    base_url: Optional[str] = None
    
    # Model Configuration
    text_model: str = "gemini-1.5-flash"
    vision_model: str = "gemini-1.5-flash"
    image_model: str = "imagen-3.0-generate-001"
    
    # Request Configuration
    timeout: float = 60.0
    max_retries: int = 3
    retry_delay: float = 1.0
    
    # Generation Parameters
    temperature: float = 0.7
    max_output_tokens: int = 2048
    top_p: float = 0.9
    top_k: int = 40
    
    # Fallback Configuration
    florence_enabled: bool = True
    florence_model: str = "multimodalart/Florence-2-large-no-flash-attn"
    florence_device: str = "cuda"
    florence_dtype: str = "float16"

@dataclass
class BFLConfig:
    """Black Forest Labs (BFL) configuration for FLUX models."""
    # API Configuration
    api_key: Optional[str] = None
    api_endpoint: str = "https://api.bfl.ml"
    
    # Model Preferences
    default_model: str = "flux.1-pro"
    use_local_models: bool = True
    
    # Local Model Configuration
    flux_variant: str = "flux-dev"  # flux-dev or flux-schnell
    models_dir: str = "models"

class ConfigManager:
    """Centralized configuration manager for AI providers and prompts."""
    
    def __init__(self, config_dir: Optional[str] = None):
        """Initialize the configuration manager."""
        if config_dir is None:
            # Default to config directory relative to project root
            current_dir = Path(__file__).parent
            project_root = current_dir.parent
            config_dir = project_root / "config"
        
        self.config_dir = Path(config_dir)
        self._ai_config = None
        self._bfl_config = None
        self._prompts = {}
        self._load_configs()
    
    def _substitute_env_vars(self, value: Any) -> Any:
        """Recursively substitute environment variables in configuration values."""
        if isinstance(value, str):
            # Handle ${VAR:-default} syntax
            def replace_env_var(match):
                var_expr = match.group(1)
                if ":-" in var_expr:
                    var_name, default = var_expr.split(":-", 1)
                    return os.getenv(var_name, default)
                else:
                    return os.getenv(var_expr, "")
            
            return re.sub(r'\$\{([^}]+)\}', replace_env_var, value)
        elif isinstance(value, dict):
            return {k: self._substitute_env_vars(v) for k, v in value.items()}
        elif isinstance(value, list):
            return [self._substitute_env_vars(item) for item in value]
        else:
            return value
    
    def _load_yaml_with_env_substitution(self, file_path: Path) -> Dict[str, Any]:
        """Load YAML file with environment variable substitution."""
        if not file_path.exists():
            return {}
        
        with open(file_path, 'r', encoding='utf-8') as f:
            content = yaml.safe_load(f)
        
        return self._substitute_env_vars(content) if content else {}
    
    def _load_configs(self):
        """Load all configuration files."""
        # Load AI provider config
        ai_config_path = self.config_dir / "ai_provider.yaml"
        ai_config_data = self._load_yaml_with_env_substitution(ai_config_path)
        
        if ai_config_data:
            provider_config = ai_config_data.get("provider", {})

            self._ai_config = AIProviderConfig(
                api_key=provider_config.get("api_key"),
                text_model=provider_config.get("text_model", "gemini-1.5-flash"),
                vision_model=provider_config.get("vision_model", "gemini-1.5-flash"),
                temperature=float(provider_config.get("temperature", 0.7)),
                max_output_tokens=int(provider_config.get("max_output_tokens", 2048)),
                requests_per_second=float(provider_config.get("requests_per_second", 15.0))
            )
            
            # Load BFL config from same file
            bfl_config = ai_config_data.get("bfl", {})
            self._bfl_config = BFLConfig(
                api_key=bfl_config.get("api_key"),
                api_endpoint=bfl_config.get("api_endpoint", "https://api.bfl.ml"),
                default_model=bfl_config.get("default_model", "flux.1-pro"),
                use_local_models=bool(bfl_config.get("use_local_models", True)),
                flux_variant=bfl_config.get("flux_variant", "flux-dev"),
                models_dir=bfl_config.get("models_dir", "models")
            )
        else:
            # Fallback to environment variables
            self._ai_config = AIProviderConfig(
                api_key=os.getenv("GOOGLE_API_KEY") or os.getenv("GEMINI_API_KEY") or os.getenv("GOOGLE_GENAI_API_KEY"),
                text_model=os.getenv("AI_PROVIDER_DEFAULT_TEXT_MODEL", "gemini-1.5-flash"),
                vision_model=os.getenv("AI_PROVIDER_DEFAULT_VISION_MODEL", "gemini-1.5-flash"),
                temperature=float(os.getenv("AI_PROVIDER_TEMPERATURE", "0.7")),
                max_output_tokens=int(os.getenv("AI_PROVIDER_MAX_TOKENS", "2048")),
                requests_per_second=float(os.getenv("AI_PROVIDER_RATE_LIMIT", "15.0"))
            )
            
            # BFL fallback to environment variables
            self._bfl_config = BFLConfig(
                api_key=os.getenv("BFL_API_KEY"),
                api_endpoint=os.getenv("BFL_API_ENDPOINT", "https://api.bfl.ml"),
                default_model=os.getenv("BFL_DEFAULT_MODEL", "flux.1-pro"),
                use_local_models=bool(os.getenv("BFL_USE_LOCAL", "true").lower() == "true"),
                flux_variant=os.getenv("BFL_FLUX_VARIANT", "flux-dev"),
                models_dir=os.getenv("BFL_MODELS_DIR", "models")
            )
        
        # Load prompts config
        prompts_config_path = self.config_dir / "prompts.yaml"
        prompts_data = self._load_yaml_with_env_substitution(prompts_config_path)
        
        if prompts_data:
            self._load_prompts_from_data(prompts_data)
    
    def _load_prompts_from_data(self, data: Dict[str, Any]):
        """Load prompts from configuration data."""
        for category, prompts in data.items():
            if isinstance(prompts, dict):
                for prompt_key, prompt_data in prompts.items():
                    if isinstance(prompt_data, dict) and "id" in prompt_data:
                        prompt_config = PromptConfig(
                            id=prompt_data["id"],
                            description=prompt_data.get("description", ""),
                            content=prompt_data.get("content", "")
                        )
                        self._prompts[prompt_data["id"]] = prompt_config
    
    def get_ai_config(self) -> AIProviderConfig:
        """Get the AI provider configuration."""
        return self._ai_config
    
    def get_bfl_config(self) -> BFLConfig:
        """Get the Black Forest Labs configuration."""
        return self._bfl_config
    
    def get_prompt(self, prompt_id: str) -> Optional[PromptConfig]:
        """Get a prompt configuration by ID."""
        return self._prompts.get(prompt_id)
    
    def get_prompt_content(self, prompt_id: str, **kwargs) -> str:
        """Get formatted prompt content by ID."""
        prompt = self.get_prompt(prompt_id)
        if prompt:
            return prompt.format(**kwargs)
        return ""
    
    def list_prompts(self) -> Dict[str, PromptConfig]:
        """Get all available prompts."""
        return self._prompts.copy()
    
    def reload_configs(self):
        """Reload all configuration files."""
        self._prompts.clear()
        self._load_configs()

# Global configuration manager instance
_config_manager = None

def get_config_manager() -> ConfigManager:
    """Get the global configuration manager instance."""
    global _config_manager
    if _config_manager is None:
        _config_manager = ConfigManager()
    return _config_manager

def get_ai_config() -> AIProviderConfig:
    """Get the AI provider configuration."""
    return get_config_manager().get_ai_config()

def get_bfl_config() -> BFLConfig:
    """Get the Black Forest Labs configuration."""
    return get_config_manager().get_bfl_config()

def get_prompt(prompt_id: str, **kwargs) -> str:
    """Get formatted prompt content by ID."""
    return get_config_manager().get_prompt_content(prompt_id, **kwargs)
