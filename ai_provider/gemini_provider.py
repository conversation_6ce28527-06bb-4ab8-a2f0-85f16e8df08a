"""
Google Gemini AI Provider

This module provides a centralized interface for all AI operations using Google's Gemini AI.
It replaces multiple AI providers (OpenAI, Together AI, etc.) with a single, unified solution.
"""

import time
import io
from typing import Optional, Dict, Any, List, Union
from PIL import Image
import logging

try:
    from google import genai
    from google.genai import types
except ImportError:
    raise ImportError("google-genai package is required. Install with: pip install google-genai")

from .config_manager import AIProviderConfig, get_ai_config
from .exceptions import (
    APIError, RateLimitError,
    AuthenticationError, ModelNotFoundError, InvalidRequestError
)

logger = logging.getLogger(__name__)

class GeminiProvider:
    """
    Centralized AI provider using Google Gemini.
    
    This class provides a unified interface for:
    - Text generation
    - Image captioning (replaces Together AI)
    - Image generation (replaces OpenAI DALL-E)
    """
    
    def __init__(self, config: Optional[AIProviderConfig] = None):
        """Initialize the Gemini provider."""
        self.config = config or get_ai_config()
        self.client = None
        self._initialize_client()
        # Timestamp of the last successful API request (for rate limiting)
        self._last_request_time: float = 0.0
    
    def _initialize_client(self):
        """Initialize the Google GenAI client."""
        try:
            if not self.config.api_key:
                raise AuthenticationError(
                    "No API key found. Please set GOOGLE_API_KEY, GEMINI_API_KEY, or GOOGLE_GENAI_API_KEY environment variable."
                )
            
            # Use Gemini Developer API
            self.client = genai.Client(api_key=self.config.api_key)
                
            logger.info("Gemini provider initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize Gemini client: {e}")
            raise AuthenticationError(f"Failed to initialize Gemini client: {e}")
    
    def _handle_api_error(self, error: Exception) -> None:
        """Convert Gemini API errors to our custom exceptions."""
        error_message = str(error)
        
        if "rate limit" in error_message.lower():
            raise RateLimitError(error_message)
        elif "authentication" in error_message.lower() or "unauthorized" in error_message.lower():
            raise AuthenticationError(error_message)
        elif "not found" in error_message.lower():
            raise ModelNotFoundError(error_message)
        elif "invalid" in error_message.lower():
            raise InvalidRequestError(error_message)
        else:
            raise APIError(error_message)
    
    def _respect_rate_limit(self) -> None:
        """Sleep to ensure we don't exceed the configured requests per second."""
        if self.config.requests_per_second and self.config.requests_per_second > 0:
            min_interval = 1.0 / self.config.requests_per_second
            now = time.time()
            elapsed = now - getattr(self, "_last_request_time", 0.0)
            if elapsed < min_interval:
                time.sleep(min_interval - elapsed)

    def _retry_with_backoff(self, func, *args, **kwargs):
        """Execute function with exponential backoff retry."""
        last_exception = None
        
        max_retries = 3  # Default retry count
        for attempt in range(max_retries + 1):
            try:
                # Respect the configured rate limit before each attempt
                self._respect_rate_limit()
                result = func(*args, **kwargs)
                # Record the timestamp of the successful request
                self._last_request_time = time.time()
                return result
            except Exception as e:
                last_exception = e

                if attempt == max_retries:
                    break

                # Calculate delay with exponential backoff
                delay = 1.0 * (2 ** attempt)  # Default retry delay
                logger.warning(f"API call failed (attempt {attempt + 1}), retrying in {delay}s: {e}")
                time.sleep(delay)
        
        # Handle the final exception
        self._handle_api_error(last_exception)
    
    def generate_text(
        self, 
        prompt: str, 
        model: Optional[str] = None,
        temperature: Optional[float] = None,
        max_tokens: Optional[int] = None,
        **kwargs
    ) -> str:
        """
        Generate text using Gemini.
        
        Args:
            prompt: The text prompt
            model: Model to use (defaults to config.default_text_model)
            temperature: Sampling temperature
            max_tokens: Maximum tokens to generate
            **kwargs: Additional generation parameters
            
        Returns:
            Generated text string
        """
        model = model or self.config.text_model
        temperature = temperature or self.config.temperature
        max_tokens = max_tokens or self.config.max_output_tokens
        
        def _generate():
            response = self.client.models.generate_content(
                model=model,
                contents=prompt,
                config=types.GenerateContentConfig(
                    temperature=temperature,
                    max_output_tokens=max_tokens,
                    **kwargs
                )
            )
            return response.text
        
        return self._retry_with_backoff(_generate)

    def caption_image(
        self,
        image: Union[str, bytes, Image.Image],
        prompt: str = "Describe this image in detail.",
        model: Optional[str] = None,
        **kwargs
    ) -> str:
        """
        Generate image caption using Gemini Vision.

        This method replaces Together AI image captioning functionality.

        Args:
            image: Image as file path, bytes, or PIL Image
            prompt: Caption prompt (defaults to generic description)
            model: Vision model to use (defaults to config.default_vision_model)
            **kwargs: Additional generation parameters

        Returns:
            Image caption string
        """
        model = model or self.config.vision_model

        # Convert image to appropriate format
        image_data = self._prepare_image(image)

        def _caption():
            response = self.client.models.generate_content(
                model=model,
                contents=[
                    types.Part.from_text(prompt),
                    types.Part.from_bytes(image_data, mime_type="image/jpeg")
                ],
                config=types.GenerateContentConfig(
                    temperature=self.config.temperature,
                    max_output_tokens=self.config.max_output_tokens,
                    **kwargs
                )
            )
            return response.text

        return self._retry_with_backoff(_caption)

    def generate_image(
        self,
        prompt: str,
        model: Optional[str] = None,
        size: str = "1024x1024",
        quality: str = "standard",
        n: int = 1,
        **kwargs
    ) -> List[Dict[str, Any]]:
        """
        Generate images using Gemini/Vertex AI Imagen.

        This method replaces OpenAI DALL-E functionality.

        Args:
            prompt: Image generation prompt
            model: Image model to use (defaults to config.default_image_model)
            size: Image size (e.g., "1024x1024")
            quality: Image quality ("standard" or "hd")
            n: Number of images to generate
            **kwargs: Additional generation parameters

        Returns:
            List of dictionaries with image data
        """
        model = model or "imagen-3.0-generate-001"  # Default image model

        def _generate_image():
            # Note: This is a placeholder for image generation
            # Actual implementation depends on available Gemini image generation capabilities
            # May need to use Vertex AI Imagen or alternative approach

            try:
                response = self.client.models.generate_content(
                    model=model,
                    contents=f"Generate an image: {prompt}",
                    config=types.GenerateContentConfig(
                        **kwargs
                    )
                )

                # This is a simplified implementation
                # Real implementation would depend on actual Gemini image generation API
                return [{"url": None, "b64_json": None, "revised_prompt": prompt}] * n

            except Exception as e:
                # Fallback: Use text generation to create detailed prompts
                logger.warning(f"Direct image generation failed, using text enhancement: {e}")
                enhanced_prompt = self.generate_text(
                    f"Enhance this image generation prompt with detailed visual descriptions: {prompt}",
                    max_tokens=500
                )
                return [{"url": None, "b64_json": None, "revised_prompt": enhanced_prompt}] * n

        return self._retry_with_backoff(_generate_image)

    def _prepare_image(self, image: Union[str, bytes, Image.Image]) -> bytes:
        """Convert image to bytes format for API calls."""
        if isinstance(image, str):
            # File path
            with open(image, 'rb') as f:
                return f.read()
        elif isinstance(image, bytes):
            # Already bytes
            return image
        elif isinstance(image, Image.Image):
            # PIL Image
            buffer = io.BytesIO()
            image.save(buffer, format='JPEG')
            return buffer.getvalue()
        else:
            raise InvalidRequestError(f"Unsupported image type: {type(image)}")

    def health_check(self) -> bool:
        """Check if the provider is healthy and accessible."""
        try:
            # Simple test generation
            self.generate_text("Hello", max_tokens=10)
            return True
        except Exception as e:
            logger.error(f"Health check failed: {e}")
            return False
