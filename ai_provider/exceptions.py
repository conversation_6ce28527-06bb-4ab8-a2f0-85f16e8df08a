"""
Custom exceptions for the AI provider service.
"""

class AIProviderError(Exception):
    """Base exception for AI provider errors."""
    pass

class APIError(AIProviderError):
    """Exception raised for API-related errors."""
    
    def __init__(self, message, code=None, response=None):
        super().__init__(message)
        self.code = code
        self.response = response
        self.message = message

class RateLimitError(APIError):
    """Exception raised when rate limits are exceeded."""
    pass

class AuthenticationError(APIError):
    """Exception raised for authentication errors."""
    pass

class ModelNotFoundError(APIError):
    """Exception raised when a requested model is not found."""
    pass

class InvalidRequestError(APIError):
    """Exception raised for invalid requests."""
    pass
