import os
import sys
import argparse
from pathlib import Path
from PIL import Image

# Import the centralized AI provider and config
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from ai_provider import get_provider
from ai_provider.config_manager import get_prompt

from caption import (
    get_system_prompt,
    extract_captions,
    images_to_base64
)

def optimize_prompt(user_prompt, captions_dir=None, captions_list=None, reference_image=None):
    """Optimize a user prompt to follow the same format as training captions.
    
    Args:
        user_prompt (str): The simple user prompt to optimize
        captions_dir (str, optional): Directory containing caption .txt files
        captions_list (list, optional): List of captions to use instead of loading from files
        reference_image (str, optional): Path to a reference image for outfit consistency
    """
    all_captions = []
    if captions_list:
        all_captions = captions_list
    elif captions_dir:
        # Collect all captions from text files in the directory
        captions_path = Path(captions_dir)
        for file_path in captions_path.glob("*.txt"):
            captions = extract_captions(file_path)
            all_captions.extend(captions)

    if not all_captions:
        raise ValueError("Please provide either caption files or a list of captions!")

    # Concatenate all captions with newlines
    captions_text = "\n".join(all_captions)

    provider = get_provider()
    
    # Process reference image if provided
    outfit_description = None
    if reference_image:
        try:
            ref_img = Image.open(reference_image).convert("RGB")
            ref_img_base64 = images_to_base64([ref_img])[0]
            
            # Get outfit description from reference image
            print("Generating outfit description from reference image...")
            # Use centralized provider for outfit description
            outfit_prompt = get_prompt("outfit_description")
            if not outfit_prompt:
                outfit_prompt = """Analyze this image and provide a detailed description of the person's outfit and style. Focus on:

- Clothing items (tops, bottoms, outerwear, accessories)
- Colors and patterns
- Style and fit
- Materials and textures
- Overall aesthetic

Provide a concise but comprehensive description that could be used to maintain outfit consistency across multiple images."""

            outfit_description = provider.caption_image(ref_img_base64, outfit_prompt)
            print(f"Using outfit description: '{outfit_description}'")
        except Exception as e:
            print(f"Error processing reference image: {e}")
    
    # Build the content for the API request
    content = (
        f"These are all of the captions used to train the LoRA:\n\n"
        f"{captions_text}\n\n"
    )
    
    # Add outfit description if available
    if outfit_description:
        content += f"Use this outfit description for the character: {outfit_description}\n\n"
    
    content += f"Now optimize this prompt to follow the caption format used in training: {user_prompt}"
    
    # Get system prompt from centralized config
    system_prompt = get_prompt("lora_optimization_system")
    if not system_prompt:
        # Fallback to original system prompt
        system_prompt = get_system_prompt()

    # Create full prompt for Gemini
    full_prompt = f"{system_prompt}\n\n{content}"

    # Generate optimized prompt using Gemini
    optimized_prompt = provider.generate_text(full_prompt)
    return optimized_prompt


def main():
    parser = argparse.ArgumentParser(description='Optimize prompts based on existing captions.')
    parser.add_argument('--prompt', type=str, required=True, help='User prompt to optimize')
    parser.add_argument('--captions', type=str, required=True, help='Directory containing caption .txt files')
    parser.add_argument('--reference_image', type=str, help='Path to a reference image for outfit consistency')

    args = parser.parse_args()
    if not os.path.isdir(args.captions):
        print(f"Error: Captions directory '{args.captions}' does not exist.")
        return

    try:
        optimized_prompt = optimize_prompt(args.prompt, args.captions, reference_image=args.reference_image)
        print("\nOptimized Prompt:")
        print(optimized_prompt)

    except Exception as e:
        print(f"Error optimizing prompt: {e}")


if __name__ == "__main__":
    main()
