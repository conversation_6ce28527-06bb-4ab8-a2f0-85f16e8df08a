# Use a newer version of Ubuntu that has the required GLIBC version
FROM ubuntu:22.04

# Avoid interactive prompts during installation
ENV DEBIAN_FRONTEND=noninteractive

# Install dependencies for acli
RUN apt-get update && apt-get install -y \
    wget \
    gnupg2 \
    git \
    nano \
    && rm -rf /var/lib/apt/lists/*

# Set up the APT repository for acli
RUN mkdir -p -m 755 /etc/apt/keyrings && \
    wget -nv -O- https://acli.atlassian.com/gpg/public-key.asc | gpg --dearmor -o /etc/apt/keyrings/acli-archive-keyring.gpg && \
    chmod go+r /etc/apt/keyrings/acli-archive-keyring.gpg && \
    echo "deb [arch=$(dpkg --print-architecture) signed-by=/etc/apt/keyrings/acli-archive-keyring.gpg] https://acli.atlassian.com/linux/deb stable main" > /etc/apt/sources.list.d/acli.list

# Install acli
RUN apt-get update && apt-get install -y acli

# Set the working directory
WORKDIR /workspace

# Set the entrypoint to acli
ENTRYPOINT ["acli"]
