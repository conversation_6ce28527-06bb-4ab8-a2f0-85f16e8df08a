# Environment Variables for XChar Project
# Copy this file to .env and fill in your API keys

# Google Gemini API Key (Primary - used for all AI operations)
# Get your API key from: https://aistudio.google.com/app/apikey
GOOGLE_API_KEY=your_google_api_key_here

# Alternative Gemini API Key name (also supported)
# GEMINI_API_KEY=your_gemini_api_key_here

# Google Cloud Project (Optional - for Vertex AI)
# Only needed if using Vertex AI instead of Gemini Developer API
# GOOGLE_CLOUD_PROJECT=your_project_id
# GOOGLE_CLOUD_LOCATION=us-central1

# Hugging Face Token (for model downloads)
HF_TOKEN=your_huggingface_token_here

# AI Provider Configuration (Optional)
# Model Selection - you can override the default models
# AI_PROVIDER_DEFAULT_TEXT_MODEL=gemini-1.5-flash
# AI_PROVIDER_DEFAULT_VISION_MODEL=gemini-1.5-flash

# Generation Parameters (Optional)
# AI_PROVIDER_TEMPERATURE=0.7
# AI_PROVIDER_MAX_TOKENS=2048
# AI_PROVIDER_TIMEOUT=60.0
# AI_PROVIDER_MAX_RETRIES=3
# AI_PROVIDER_RETRY_DELAY=1.0
# AI_PROVIDER_TEMPERATURE=0.7
# AI_PROVIDER_MAX_TOKENS=2048
# AI_PROVIDER_TOP_P=0.9
# AI_PROVIDER_TOP_K=40

# Request Configuration
# AI_PROVIDER_TIMEOUT=60.0
# AI_PROVIDER_MAX_RETRIES=3
# AI_PROVIDER_RETRY_DELAY=1.0

# Generation Parameters
# AI_PROVIDER_TEMPERATURE=0.7
# AI_PROVIDER_MAX_TOKENS=2048
# AI_PROVIDER_TOP_P=0.9
# AI_PROVIDER_TOP_K=40

# Legacy API Keys (No longer used - migrated to Google Gemini)
# These are kept for reference but are not used by the application
# TOGETHER_API_KEY=deprecated_together_api_key
# OPENAI_API_KEY=deprecated_openai_api_key

# Model Paths (Optional)
# MODELS_PATH=/path/to/your/models

# Docker Configuration (Optional)
# AI_TOOLKIT_AUTH=password
