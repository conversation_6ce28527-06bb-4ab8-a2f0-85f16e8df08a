#!/usr/bin/env python

import os
import subprocess
import time
from setuptools import find_packages, setup
from typing import List, Dict, Tuple, Any  # For type hinting the setup script itself

# --- Configuration for ScaleX ---
PACKAGE_NAME = "scalex"  # New package name
VERSION_FILENAME = "VERSION"  # Assumes a VERSION file exists in the root
VERSION_PY_FILE = f"{PACKAGE_NAME}/version.py"  # Path to the generated version.py
README_FILE = "README.md"
REQUIREMENTS_FILE = "requirements.txt"
# --------------------------------


def readme() -> str:
    """Reads the README file."""
    with open(README_FILE, encoding="utf-8") as f:
        content = f.read()
    return content


def get_git_hash() -> str:
    """Gets the current git hash."""

    def _minimal_ext_cmd(cmd: List[str]) -> bytes:
        # construct minimal environment
        env: Dict[str, str] = {}
        for k in ["SYSTEMROOT", "PATH", "HOME"]:  # Common environment variables
            v = os.environ.get(k)
            if v is not None:
                env[k] = v
        env["LANGUAGE"] = "C"
        env["LANG"] = "C"
        env["LC_ALL"] = "C"
        out = subprocess.Popen(cmd, stdout=subprocess.PIPE, env=env).communicate()[0]
        return out

    try:
        git_output = _minimal_ext_cmd(["git", "rev-parse", "HEAD"])
        sha = git_output.strip().decode("ascii")
    except OSError:
        sha = "unknown"
    return sha


def get_short_hash() -> str:
    """Gets the short git hash (first 7 characters)."""
    if os.path.exists(".git"):  # Check if in a git repository
        sha = get_git_hash()[:7]
    else:
        sha = "unknown"
    return sha


def get_project_version() -> Tuple[str, str]:
    """Reads the version from the VERSION file."""
    try:
        with open(VERSION_FILENAME, "r") as f:
            short_version = f.read().strip()
    except FileNotFoundError:
        print(f"Warning: {VERSION_FILENAME} not found. Defaulting version to '0.0.0'.")
        short_version = "0.0.0"  # Default if VERSION file is missing

    # Create a simple version info string for display/tuple
    # e.g., "1.0.0" -> ('1', '0', '0')
    version_info_str = ", ".join(
        [f'"{x}"' if not x.isdigit() else x for x in short_version.split(".")]
    )
    return short_version, version_info_str


def write_version_py() -> None:
    """Generates the version.py file with version and git hash."""
    content = """# GENERATED VERSION FILE. DO NOT EDIT MANUALLY.
# Generated by setup.py at: {time_str}
__version__ = '{version_str}'
__gitsha__ = '{git_hash_str}'
version_info = ({version_info_tuple_str}) # type: ignore
"""
    short_version, version_info_tuple_str = get_project_version()
    git_hash_str = get_short_hash()

    time_str = time.asctime()
    version_py_content = content.format(
        time_str=time_str,
        version_str=short_version,
        git_hash_str=git_hash_str,
        version_info_tuple_str=version_info_tuple_str,
    )

    # Ensure the package directory exists before writing version.py
    os.makedirs(PACKAGE_NAME, exist_ok=True)
    with open(VERSION_PY_FILE, "w") as f:
        f.write(version_py_content)
    print(f"Generated {VERSION_PY_FILE} with version {short_version}")


def get_runtime_version() -> str:
    """Gets the version from the generated version.py file for setup."""
    # Ensure version.py is generated before trying to import/exec it
    if not os.path.exists(VERSION_PY_FILE):
        write_version_py()  # Generate it if it doesn't exist (e.g., sdist)

    version_globals: Dict[str, Any] = {}
    with open(VERSION_PY_FILE, "r") as f:
        exec(compile(f.read(), VERSION_PY_FILE, "exec"), version_globals)
    return version_globals["__version__"]


def get_requirements(filename: str = REQUIREMENTS_FILE) -> List[str]:
    """Reads requirements from a file."""
    here = os.path.dirname(os.path.realpath(__file__))
    filepath = os.path.join(here, filename)
    if not os.path.exists(filepath):
        print(
            f"Warning: {filename} not found. No requirements will be installed via setup.py."
        )
        return []

    with open(filepath, "r") as f:
        # Filter out empty lines and comments
        requires = [
            line.strip() for line in f if line.strip() and not line.startswith("#")
        ]
    return requires


if __name__ == "__main__":
    # Generate version.py before setup() is called.
    # This is important for sdist to include the generated version.py.
    write_version_py()

    setup(
        name=PACKAGE_NAME,
        version=get_runtime_version(),  # Gets version from scalex/version.py
        description="ScaleX: AI Face Restoration and Enhancement Tool",  # Updated description
        long_description=readme(),
        long_description_content_type="text/markdown",
        author="md. Siam Mia",  # Update with your details
        author_email="<EMAIL>",  # Update with your details
        keywords="computer vision, pytorch, image restoration, super-resolution, face restoration, gan, generative models, AI, scalex",  # Updated keywords
        url="https://github.com/Md-Siam-Mia-Code/ScaleX",  # Update with your repo URL
        include_package_data=True,  # Includes non-code files specified in MANIFEST.in
        # find_packages() will find 'scalex' and its subpackages (scalex.archs, etc.)
        # Ensure no unwanted top-level folders are accidentally packaged as packages.
        packages=find_packages(
            exclude=[
                "options",
                "inputs",
                "results",
                "scripts",
                "tests",
                "docs",
                ".github",
            ]
        ),
        classifiers=[
            "Development Status :: 4 - Beta",  # Or adjust as needed
            "License :: OSI Approved :: Apache Software License",
            "Operating System :: OS Independent",
            "Programming Language :: Python :: 3",
            "Programming Language :: Python :: 3.9",  # Start from a reasonable modern version
            "Programming Language :: Python :: 3.10",
            "Programming Language :: Python :: 3.11",
            "Programming Language :: Python :: 3.12",  # Target version
            "Topic :: Scientific/Engineering :: Artificial Intelligence",
            "Topic :: Scientific/Engineering :: Image Processing",
        ],
        license="Apache License Version 2.0",  # Or your chosen license
        # Dependencies needed during the setup process itself (rarely needed for pure Python)
        setup_requires=[
            "numpy"
        ],  # Cython removed unless a direct dep needs it at build time
        # Runtime dependencies
        install_requires=get_requirements(),
        zip_safe=False,  # Often False for packages with data files or C extensions
    )
