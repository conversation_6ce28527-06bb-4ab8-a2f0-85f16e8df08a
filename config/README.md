# Configuration System

This directory contains the unified configuration system for the AI provider and prompts used throughout the application.

## Files

### `ai_provider.yaml`
Contains all configuration for the centralized AI provider system:

- **API Configuration**: API keys, project settings, base URLs
- **Model Selection**: Default models for text, vision, and image generation
- **Request Configuration**: Timeouts, retries, delays
- **Generation Parameters**: Temperature, token limits, sampling parameters
- **Fallback Configuration**: Florence-2 model settings for image captioning fallback

### `prompts.yaml`
Contains all prompts used throughout the system for AI generation:

- **Image Captioning Prompts**: System prompts for various captioning tasks
- **Prompt Optimization**: Prompts for LoRA prompt optimization
- **PuLID Flux Prompts**: Prompts for PuLID Flux image generation
- **Flux Train UI Prompts**: Prompts for Flux training interface

## Environment Variable Substitution

Both configuration files support environment variable substitution using the syntax:
- `${VAR_NAME}` - Use environment variable value
- `${VAR_NAME:-default}` - Use environment variable value or default if not set

## Usage

### Python Code
```python
from ai_provider.config_manager import get_ai_config, get_prompt

# Get AI provider configuration
config = get_ai_config()

# Get a specific prompt
prompt = get_prompt("training_utilities_system")

# Get a prompt with formatting
prompt = get_prompt("lora_captioner_system", trigger_word="tr1gg3r")
```

### Configuration Management
```python
from ai_provider.config_manager import get_config_manager

# Get the config manager
manager = get_config_manager()

# Reload configurations
manager.reload_configs()

# List all available prompts
prompts = manager.list_prompts()
```

## Environment Variables

The system supports the following environment variables:

### API Configuration
- `GOOGLE_API_KEY` / `GEMINI_API_KEY` / `GOOGLE_GENAI_API_KEY` - API key for Google Gemini
- `GOOGLE_PROJECT_ID` - Google Cloud project ID
- `GOOGLE_LOCATION` - Google Cloud location (default: us-central1)
- `GEMINI_BASE_URL` - Custom base URL for Gemini API

### Model Selection
- `AI_PROVIDER_DEFAULT_TEXT_MODEL` - Default text generation model
- `AI_PROVIDER_DEFAULT_VISION_MODEL` - Default vision/captioning model
- `AI_PROVIDER_DEFAULT_IMAGE_MODEL` - Default image generation model

### Request Configuration
- `AI_PROVIDER_TIMEOUT` - Request timeout in seconds
- `AI_PROVIDER_MAX_RETRIES` - Maximum number of retries
- `AI_PROVIDER_RETRY_DELAY` - Delay between retries in seconds

### Generation Parameters
- `AI_PROVIDER_TEMPERATURE` - Sampling temperature
- `AI_PROVIDER_MAX_TOKENS` - Maximum output tokens
- `AI_PROVIDER_TOP_P` - Top-p sampling parameter
- `AI_PROVIDER_TOP_K` - Top-k sampling parameter

## Prompt IDs

### Image Captioning
- `training_utilities_system` - System prompt for training utilities
- `training_utilities_caption` - Main captioning prompt for training utilities
- `lora_captioner_system` - System prompt for LoRA captioning
- `lora_captioner_caption` - Main captioning prompt for LoRA
- `outfit_description` - Prompt for outfit description extraction
- `flux_train_caption` - Captioning prompt for Flux training UI

### Prompt Optimization
- `lora_optimization_system` - System prompt for LoRA prompt optimization

### PuLID Flux
- `pulid_flux_system` - System prompt for PuLID Flux prompt generation

## Migration from Hardcoded Prompts

All modules have been updated to use the centralized prompt system:

1. **training/utilities.py** - Uses `training_utilities_system`
2. **LoRACaptioner/caption.py** - Uses `lora_captioner_system`
3. **LoRACaptioner/prompt.py** - Uses `lora_optimization_system` and `outfit_description`
4. **training/pulid_flux_images.py** - Uses `pulid_flux_system`
5. **ai_toolkit/flux_train_ui.py** - Uses `flux_train_caption`

Each module includes fallback to the original hardcoded prompts if the configuration system is not available, ensuring backward compatibility.

## Benefits

1. **Centralized Management**: All prompts and configuration in one place
2. **Environment Variable Support**: Easy configuration across different environments
3. **Consistent Formatting**: Standardized prompt structure and formatting
4. **Easy Updates**: Change prompts without modifying code
5. **Fallback Support**: Graceful degradation if config files are missing
6. **Version Control**: Track prompt changes through git history
