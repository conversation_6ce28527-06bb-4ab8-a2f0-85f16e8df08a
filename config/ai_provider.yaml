# AI Provider Configuration
# This file contains settings for the centralized Gemini AI provider

provider:
  # API Configuration
  api_key: ${GOOGLE_API_KEY:-${GEMINI_API_KEY:-${GOOGLE_GENAI_API_KEY}}}

  # Model Selection
  text_model: ${AI_PROVIDER_DEFAULT_TEXT_MODEL:-gemini-2.5-flash}
  vision_model: ${AI_PROVIDER_DEFAULT_VISION_MODEL:-gemini-2.5-flash}

  # Generation Parameters
  temperature: ${AI_PROVIDER_TEMPERATURE:-0.7}
  max_output_tokens: ${AI_PROVIDER_MAX_TOKENS:-2048}

  # Rate Limiting
  requests_per_second: ${AI_PROVIDER_RATE_LIMIT:-15}  # Max requests per second to avoid rate limits

# Black Forest Labs (BFL) Configuration
# For PuLID-FLUX and FLUX model API access
bfl:
  # BFL API Configuration
  api_key: ${BFL_API_KEY}
  api_endpoint: ${BFL_API_ENDPOINT:-https://api.bfl.ml}
  
  # Model Preferences
  default_model: ${BFL_DEFAULT_MODEL:-flux.1-pro}
  use_local_models: ${BFL_USE_LOCAL:-true}
  
  # Local Model Configuration
  flux_variant: ${BFL_FLUX_VARIANT:-flux-dev}  # flux-dev or flux-schnell
  models_dir: ${BFL_MODELS_DIR:-models}
