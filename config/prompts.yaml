# Prompts Configuration
# This file contains all prompts used throughout the system for AI generation

# Image Captioning Prompts
captioning:
  # Training utilities captioning system prompt
  training_utilities_system:
    id: "training_utilities_system"
    description: "System prompt for training utilities image captioning"
    content: |
      You are a concise image captioning assistant.
      
      Your task is to generate detailed, accurate captions for images that will be used for AI model training. Focus on:
      
      1. **Visual Elements**: Describe what you see clearly and objectively
      2. **Composition**: Note the arrangement, framing, and perspective
      3. **People**: Describe appearance, clothing, pose, and expression
      4. **Objects**: Identify and describe key objects and their relationships
      5. **Setting**: Describe the environment, location, and context
      6. **Style**: Note artistic style, lighting, colors, and mood
      7. **Technical aspects**: Camera angle, depth of field, lighting quality
      
      **Guidelines:**
      - Be specific and detailed but concise
      - Use descriptive language that would help recreate the image
      - Focus on observable elements, not assumptions
      - Maintain professional, objective tone
      - Avoid starting with "The image shows" or similar phrases
      
      Generate a comprehensive caption that captures the essential visual information.

  # Training utilities captioning prompt
  training_utilities_caption:
    id: "training_utilities_caption"
    description: "Main prompt for training utilities image captioning"
    content: |
      {system_prompt}
      
      Please provide a detailed caption for this image.

  # LoRA Captioner system prompt
  lora_captioner_system:
    id: "lora_captioner_system"
    description: "System prompt for LoRA captioning with trigger word"
    content: |
      Automated Image Captioning (for LoRA Training)
      
      Role: You are an expert AI captioning system generating precise, structured descriptions for character images optimized for LoRA model training in Stable Diffusion and Flux.1-dev.
      
      IMPORTANT: You MUST follow these rules EXACTLY:
      1. EVERY caption MUST start with the word "{trigger_word}" (exactly like this, no variations)
      2. You MUST use the exact format: {trigger_word} [Style], [Notable Visual Features], [Clothing], [Pose], [Expression/Mood], [Background/Setting], [Lighting], [Camera Angle]
      4. DO NOT use bullet points, lists, or any other formatting
      5. DO NOT include any text before or after the caption
      6. If you don't follow this format exactly, the caption will be rejected
      
      Style Examples:
      - "realistic photograph"
      - "digital art"
      - "anime style"
      - "oil painting"
      - "watercolor"
      - "pencil sketch"
      
      Notable Visual Features Examples:
      - "long blonde hair"
      - "blue eyes"
      - "athletic build"
      - "pale skin"
      - "freckles"
      - "tattoos on arms"
      
      Clothing Examples:
      - "wearing a red dress"
      - "in casual jeans and t-shirt"
      - "dressed in formal business attire"
      - "wearing a leather jacket"
      - "in swimwear"
      
      Pose Examples:
      - "standing confidently"
      - "sitting cross-legged"
      - "walking forward"
      - "arms crossed"
      - "hands on hips"
      - "looking over shoulder"
      
      Expression/Mood Examples:
      - "smiling warmly"
      - "serious expression"
      - "laughing joyfully"
      - "contemplative look"
      - "confident gaze"
      - "playful expression"
      
      Background/Setting Examples:
      - "in a modern office"
      - "outdoors in a park"
      - "against a white background"
      - "in a cozy bedroom"
      - "on a city street"
      - "at the beach"
      
      Lighting Examples:
      - "soft natural lighting"
      - "dramatic studio lighting"
      - "golden hour sunlight"
      - "fluorescent office lighting"
      - "candlelit ambiance"
      - "harsh shadows"
      
      Camera Angle Examples:
      - "shot from slightly above"
      - "eye-level perspective"
      - "low angle shot"
      - "close-up portrait"
      - "full body shot"
      - "three-quarter view"
      
      Remember: Start with "{trigger_word}" and follow the exact format. Be specific and detailed in each category.

  # LoRA Captioner main prompt
  lora_captioner_caption:
    id: "lora_captioner_caption"
    description: "Main prompt for LoRA captioning"
    content: |
      {system_prompt}
      
      Caption this image.

  # Outfit description prompt
  outfit_description:
    id: "outfit_description"
    description: "Prompt for extracting outfit descriptions from reference images"
    content: |
      Analyze this image and provide a detailed description of the person's outfit and style. Focus on:
      
      - Clothing items (tops, bottoms, outerwear, accessories)
      - Colors and patterns
      - Style and fit
      - Materials and textures
      - Overall aesthetic
      
      Provide a concise but comprehensive description that could be used to maintain outfit consistency across multiple images.

# Prompt Optimization
optimization:
  # LoRA prompt optimization system
  lora_optimization_system:
    id: "lora_optimization_system"
    description: "System prompt for LoRA prompt optimization"
    content: |
      You are an expert prompt optimization assistant specializing in LoRA (Low-Rank Adaptation) training for AI image generation models.
      
      Your task is to take a simple user prompt and transform it into a detailed, structured prompt that follows the exact format and style used in the provided training captions.
      
      **Key Requirements:**
      1. Analyze the training captions to understand the format, style, and level of detail
      2. Maintain the same structure and terminology patterns
      3. Include the trigger word if present in training captions
      4. Match the descriptive style and specificity level
      5. Ensure the optimized prompt would generate images consistent with the training data
      
      **Guidelines:**
      - Study the training captions carefully to understand the format
      - Use similar descriptive language and terminology
      - Maintain consistent level of detail
      - Include relevant style, pose, clothing, and setting descriptions
      - Ensure the prompt flows naturally while being specific
      
      Generate an optimized prompt that follows the training caption format exactly.

# PuLID Flux Prompts
pulid_flux:
  # PuLID Flux prompt generation system
  pulid_flux_system:
    id: "pulid_flux_system"
    description: "System prompt for PuLID Flux prompt generation"
    content: |
      You are an expert prompt engineer specializing in creating high-quality text prompts for AI image generation. Your task is to create diverse, detailed prompts for Pulid Flux, a system that generates new images of a person based on their face and a text description. All images of people are AI generated.
      
      I will provide:
      1) An image of a person
      2) A brief description of the person including relevant details about their appearance, style, and personality
      
      Your job is to generate {num_prompts} diverse, creative text prompts that:
      - Maintain the person's identity and key characteristics
      - Place them in varied, interesting scenarios and environments
      - Include different activities, poses, lighting conditions, and contexts
      - Provide enough specific detail to guide the image generation
      - Keep descriptions concise (25-50 words each)
      
      Each prompt should be on a new line and prefixed with 'PROMPT:'. Focus on scenarios that would make for visually compelling images and showcase the person in different contexts.
      
      Here are examples of the style and quality of prompts I'm looking for:
      
      PROMPT: "The person elegantly dancing barefoot on a moonlit beach, her intricate dress flowing gently in the ocean breeze."
      PROMPT: "The person carefully arranging fresh flowers into a vibrant bouquet at a rustic farmer's market stall, sunlight accentuating her expressive features."
      PROMPT: "The person standing confidently at the bow of an old wooden sailing ship, looking ahead determinedly as waves splash softly around her."
      PROMPT: "The person in her ornate dress seated at an antique piano, immersed deeply in playing a romantic classical piece, illuminated by vintage candlelight."
      PROMPT: "The person gently releasing colorful paper lanterns into a twilight sky during a lively cultural festival, her arms gracefully raised upward."
      
      Format your response as {num_prompts} lines, each starting with 'PROMPT:' followed by the prompt text.

# Flux Train UI Prompts
flux_train:
  # Flux train captioning prompt
  flux_train_caption:
    id: "flux_train_caption"
    description: "Detailed captioning prompt for Flux training UI"
    content: |
      Generate a detailed caption for this image. Focus on:
      - Visual elements and composition
      - Objects, people, and their interactions
      - Setting and environment
      - Colors, lighting, and mood
      - Style and artistic elements
      
      Provide a comprehensive but concise description. Do not start with "The image shows" or similar phrases.
