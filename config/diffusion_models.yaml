# Centralized Diffusion Model Configuration
# ---------------------------------------------------------------
# Edit this file to control which checkpoints / LoRAs / schedulers
# each workflow uses and where they are downloaded to.
# ---------------------------------------------------------------

api_keys:
  # HuggingFace access token (if required for gated repos)
  huggingface: ${HF_TOKEN:-""}
  # Civitai API key for authenticated downloads
  civitai: ${CIVITAI_API_KEY:-""}

# ---------------------------------------------------------------
# MV-Adapter settings
# ---------------------------------------------------------------
mv_adapter:
  base_model: "stabilityai/stable-diffusion-xl-base-1.0"
  vae_model: "madebyollin/sdxl-vae-fp16-fix"
  unet_model: null               # set to repo/path or keep null
  lora_models: []                # list[str] of repo paths or local files
  controlnet_model: null         # ControlNet repo/path if needed
  scheduler: "CompVis/scheduler-foo"
  download_dir: "models/mv_adapter"  # where HF will cache / save weights

# ---------------------------------------------------------------
# PuLID-FLUX settings
# ---------------------------------------------------------------
pulid_flux:
  flux_variant: "flux-dev"       # flux-dev | flux-schnell
  models_dir: "models"          # local directory for model weights
  flow_repo: null                # override HF repo for FLUX transformer
  ae_repo: null                  # override HF repo for AutoEncoder
  use_local: true                # if false, prefer remote API

# ---------------------------------------------------------------
# Civitai model list (used by install.py & toolkit)
# ---------------------------------------------------------------
civitai:
  model_list:
    - id: 782002
      revision: "full-fp16"
      filename: "juggernaut-xl.safetensors"
    - id: 90072
      revision: "pruned-fp16"
      filename: "some-lora.safetensors"

# ---------------------------------------------------------------
# ComfyUI reference paths (workflow itself remains untouched)
# ---------------------------------------------------------------
comfyui:
  checkpoints_dir: "${COMFYUI_PATH}/models/checkpoints"
  loras_dir: "${COMFYUI_PATH}/models/loras"
  default_checkpoint: "juggernaut-xl.safetensors"
  extra_models: []  # additional model URLs or repos to prefetch
