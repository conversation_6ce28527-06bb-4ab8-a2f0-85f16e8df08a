# AI Provider Migration Guide

This project has been migrated from multiple AI providers (Together AI, OpenAI) to a single, centralized system using **Google Gemini AI**.

## What Changed

### Before (Multiple Providers)
- **Together AI**: Used for image captioning with hardcoded Llama models
- **OpenAI**: Used for image generation with hardcoded DALL-E models
- **Florence-2**: Used for AI captioning in training UI
- **Multiple API keys**: `TOGETHER_API_KEY`, `OPENAI_API_KEY`, etc.
- **Hardcoded models**: Fixed model names like `meta-llama/Llama-4-Maverick-17B-128E-Instruct-FP8`
- **Different interfaces**: Each provider had its own API structure

### After (Centralized Gemini)
- **Google Gemini**: Single provider for all AI operations
- **One API key**: `GOOGLE_API_KEY` or `GEMINI_API_KEY`
- **Configurable models**: All models can be customized via environment variables
- **Unified interface**: All AI operations go through the centralized provider
- **Better performance**: Gemini Pro Vision for image captioning, Gemini Pro for text generation

## Migration Steps

### 1. Get Google Gemini API Key
1. Visit [Google AI Studio](https://aistudio.google.com/app/apikey)
2. Create a new API key
3. Copy the API key for use in your environment

### 2. Update Environment Variables
Replace your old API keys with the new Google API key:

```bash
# Old (no longer used)
TOGETHER_API_KEY=your_together_key
OPENAI_API_KEY=your_openai_key

# New (required)
GOOGLE_API_KEY=your_google_api_key
```

### 3. Update Your .env File
1. Copy `.env.example` to `.env`
2. Fill in your `GOOGLE_API_KEY`
3. Remove old API keys (they're no longer needed)

### 4. Install Dependencies
The migration includes the new `google-genai` package:

```bash
pip install -r base_requirements.txt
pip install -r LoRACaptioner/requirements.txt
```

## What Still Works

### ✅ All Existing Functionality Preserved
- **Image captioning**: Same quality or better with Gemini Pro Vision
- **Training workflows**: All existing scripts work unchanged
- **LoRA captioning**: Same interface, better results
- **Function signatures**: All existing function calls work the same

### ✅ Same Commands
```bash
# These still work exactly the same
./scripts/run_captioner.sh /path/to/images /path/to/output
./scripts/run_ai_toolkit.sh /path/to/config.yaml
```

### ✅ Same Code Interface
```python
# This still works in your code
from training.utilities import generate_caption
caption = generate_caption(image)  # Now uses Gemini instead of Together AI
```

## Architecture

### Direct Gemini Integration
The migration uses **direct Gemini integration** throughout the codebase:

- **Centralized Provider**: All AI operations handled by `GeminiProvider`
- **Unified Configuration**: YAML-based settings in `config/ai_provider.yaml`
- **Centralized Prompts**: All prompts managed in `config/prompts.yaml`
- **Direct API Calls**: No compatibility layers - direct Google Gemini API usage
- **Simplified Error Handling**: Unified retry logic and error management

### No Compatibility Layers
Unlike some migrations that use compatibility wrappers, this implementation:
- Integrates Gemini directly into each module
- Removes unnecessary abstraction layers
- Provides better performance and reliability
- Simplifies debugging and maintenance

## Benefits of Migration

### 🚀 Performance Improvements
- **Faster responses**: Gemini typically responds faster than Together AI
- **Better quality**: Gemini Pro Vision often provides more accurate image captions
- **More reliable**: Single provider reduces API complexity and failure points

### 💰 Cost Efficiency
- **Unified billing**: Single API key and billing account
- **Competitive pricing**: Gemini offers competitive rates
- **No rate limit conflicts**: Single provider means unified rate limiting

### 🔧 Maintenance Benefits
- **Simplified configuration**: One API key instead of multiple
- **Unified error handling**: Consistent error messages and retry logic
- **Easier debugging**: Single provider means simpler troubleshooting

## Troubleshooting

### Common Issues

#### "No API key found" Error
```
AuthenticationError: No API key found. Please set GOOGLE_API_KEY, GEMINI_API_KEY, or GOOGLE_GENAI_API_KEY environment variable.
```
**Solution**: Set your Google API key in the environment or .env file.

#### "Rate limit exceeded" Error
```
RateLimitError: rate limit exceeded
```
**Solution**: The system automatically retries with exponential backoff. If this persists, check your API quota in Google AI Studio.

#### Import Errors
```
ImportError: google-genai package is required
```
**Solution**: Install the required package:
```bash
pip install google-genai
```

### Getting Help
1. Check your API key is valid in [Google AI Studio](https://aistudio.google.com/app/apikey)
2. Verify your .env file has the correct format
3. Check the logs for detailed error messages
4. Ensure you have the latest dependencies installed

## Advanced Configuration

### Using Vertex AI (Optional)
If you prefer to use Vertex AI instead of the Gemini Developer API:

```bash
GOOGLE_CLOUD_PROJECT=your_project_id
GOOGLE_CLOUD_LOCATION=us-central1
```

### Custom Model Configuration
You can customize which models are used:

```bash
AI_PROVIDER_DEFAULT_TEXT_MODEL=gemini-1.5-flash
AI_PROVIDER_DEFAULT_VISION_MODEL=gemini-1.5-flash
AI_PROVIDER_TEMPERATURE=0.7
AI_PROVIDER_MAX_TOKENS=2048
```

## Rollback (If Needed)

If you need to rollback to the old providers, you would need to:

1. Restore the old API keys in environment variables
2. Reinstall the old provider packages (`together`, `openai`)
3. Manually revert the import statements and function calls in the affected files
4. Restore the original hardcoded prompts and model references

**Note**: This migration uses direct integration (no compatibility layers), so rollback requires manual code changes. We recommend using the new Gemini-based system as it provides better performance and reliability.
