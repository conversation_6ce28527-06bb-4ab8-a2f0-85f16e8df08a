from setuptools import find_packages, setup

setup(
    name="mv-adapter",
    version="0.1.0",
    description="MV-Adapter: A Multi-View Adapter for 3D Generation",
    author="Zeh<PERSON> Huang",
    packages=find_packages(),
    install_requires=[
        "torch>=2.0.0",
        "torchvision",
        "controlnet_aux",
        "diffusers==0.31.0",
        "transformers==4.46.3",
        "peft",
        "numpy",
        "huggingface_hub==0.29.1",
        "accelerate==1.1.1",
        "opencv-python",
        "safetensors",
        "pillow",
        "omegaconf",
        "trimesh",
        "einops",
        "gradio",
        "timm",
        "kornia",
        "scikit-image",
        "sentencepiece",
        "pytorch-lightning",
        "spandrel",
        "open3d",
        "pymeshlab",
    ],
    python_requires=">=3.10",
    classifiers=[
        "Development Status :: 3 - Alpha",
        "Intended Audience :: Science/Research",
        "License :: OSI Approved :: MIT License",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.10",
    ],
    long_description=open("README.md").read(),
    long_description_content_type="text/markdown",
    url="https://github.com/huanngzh/MV-Adapter",
    project_urls={
        "Project Page": "https://huanngzh.github.io/MV-Adapter-Page/",
        "Paper": "https://arxiv.org/abs/2412.03632",
        "Model Weights": "https://huggingface.co/huanngzh/mv-adapter",
    },
)
